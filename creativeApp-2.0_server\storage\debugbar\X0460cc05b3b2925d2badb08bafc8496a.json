{"__meta": {"id": "X0460cc05b3b2925d2badb08bafc8496a", "datetime": "2025-08-19 17:04:47", "utime": **********.211845, "method": "PUT", "uri": "/api/teams/43", "ip": "127.0.0.1"}, "php": {"version": "8.0.30", "interface": "cli-server"}, "messages": {"count": 16, "messages": [{"message": "[17:04:47] LOG.info: Authenticated user roles: [\n    \"super-admin\"\n]", "message_html": null, "is_string": false, "label": "info", "time": **********.18209, "xdebug_link": null, "collector": "log"}, {"message": "[17:04:47] LOG.info: Authenticated User: {\n    \"user_id\": 150,\n    \"fname\": \"<PERSON><PERSON>\",\n    \"lname\": \"Shaikh\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.183579, "xdebug_link": null, "collector": "log"}, {"message": "[17:04:47] LOG.info: Request Files: {\n    \"files\": []\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.183661, "xdebug_link": null, "collector": "log"}, {"message": "[17:04:47] LOG.info: Name from input: {\n    \"name\": \"NeverG<PERSON>U<PERSON>\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.183733, "xdebug_link": null, "collector": "log"}, {"message": "[17:04:47] LOG.info: Icon from input: {\n    \"icon\": \"data:image\\/png;base64,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\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.183893, "xdebug_link": null, "collector": "log"}, {"message": "[17:04:47] LOG.info: Logo from input: {\n    \"logo\": \"data:image\\/png;base64,iVBORw0KGgoAAAANSUhEUgAAAgAAAAIACAYAAAD0eNT6AAAACXBIWXMAAA7DAAAOwwHHb6hkAAAAGXRFWHRTb2Z0d2FyZQB3d3cuaW5rc2NhcGUub3Jnm+48GgAAIABJREFUeJzt3Xm8ZkV54PFfbzRNszc7CjTKIruCArIpCDEqCCaYUYM6rjGYGE2MZkxmGKPRTGKUiVlc4qhoEoOjRnBGEHED0cgiq+z70tDNTnezdff88dw7fYG7vH3vW\\/Wc5ff9fJ5PNwqn6j3rc6pOVYEkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZIkSZKkXpuVXQFJRWwD7ARsDSwCtgC2BDYe+f\\/XBxaM\\/H0l8OjI3x8ClgLLgHuBJcDNwN0V6iypIhMAqb3mALsA+4zEXiP\\/vJi1D\\/dhWQncBFwHXA5cNvLndcCqIZclqQITAKk9FgKHAC8e+fNAYKPUGsHDwM+A80fip8CK1BpJktQBzwPeB5xNNNOvaXisBM4C3gvsXmB\\/SJLUWYuBDwBXkf9An2ncCHycSGQkSdLTbAb8PnAp+Q\\/tUvFL4N3ApkPaZ5IktdZBwGlE03n2A7pWrAC+THzDIElSb8wGjgW+R\\/7DODsuBN5IjGiQJKmT5gBvIobOZT94mxbXACdhIiBJ6pBZwInA1eQ\\/aJsev8IWAUlSB7wIuID8B2vb4iLgsGnsb0mSUu0IfA1YTf7DtK2xGvhn4NnruO8lSapuNvAOYoa87AdoV2I5MS+C3QKSpEbam5gWN\\/uB2dW4GHjBwEdDkqTCZgHvAR4j\\/yHZ9XgcOIVoaZEkKc2zgXPJfzD2Lb4PPGuA4yNJ0tC9CriP\\/IdhX2MZ8PIpj5IkSUMyi\\/gobRX5D8G+x2pisSG7BCRJRW0CfIf8B5\\/x1Pg2sNEkx02SpGnbHriE\\/IedMX5cDuww4dGTJGka9gFuI\\/8hZ0wedwL7T3AMJUlaJy\\/FiX3aFA8BR4x7JCVJGtCvE+vXZz\\/UjHWLR4HjxjmekiRN6QSc3KfN8Shw\\/DOOqiRJk\\/g14gGS\\/RAzZhaPE\\/M1SJI0paOAleQ\\/vIzhxGNEQidJ0oQOBh4h\\/6FlDDceBg5E0v83K7sCUoPsDFwAbJVdERWxDHgxcF12RaQmMAGQwiLgfGC37IqoqBuAg4hkQOo158+WYB7wTXz498FzgNOJYy712pzsCkgNcCpwYnYlVM1OxLoBZyXXQ5KU6PXkf6Bm5MQbkXrMbwDUZ3sBPwc2yK6IUiwHXgRclV0RKYMJgPpqPvHw3ze7Ikp1BfBCYtInqVf8BkB99Qng1dmVULqtgAXA2dkVkSSVdzSwmvw+aKMZsYqY\\/VHqFbsA1DcbAJcTk\\/5Io24hvgl5JLsiUi12Aahv\\/opY4lcaa1PiuxC7AtQbtgCoTw4kZvsz8dV4VhGzBF6YXRGpBhMA9cVs4GfEF9\\/SRC4ihgauzq6IVJpTAasv3oQPf01tf+AN2ZWQarAFQH2wIXAtsG12RdQKdwC74weB6jhbANQH78eHvwa3PfC+7EpIpdkC0B7ziKFrexCr1i0GtiEmMtkK2Iz4uG3jkX\\/\\/cWKq0yeIpU+XAfcQw52uJ9ZEv5p42+myRcCNrN0v0iAeJK63+7IrUtg2xGyYi4EdiYWSdiCul03G\\/AkxW+JKYu6EZWPibuJ+cg1xT7mJuO+o4UwAmmsr4KXEV8kHAs8H1i9QzhLiw6cLgR8AFxDJQ1d8HPhAdiXUSh8F\\/jS7EkO0HnAwcV85AHgBZVrGHiXuKT8j7ifnE\\/cZNYwJQLMcCBwLvJx44Gd00SwHfgKcCXwDuCuhDsOyFfH2vzC7Imqlh4k343uzKzID2wO\\/QdxTDifnWlhDJARnjsTFI\\/+b1Ht7AX9BPKiyp0Qdb4rUnwC\\/S0yU0jb\\/nfx9aLQ7\\/oz22QJ4F\\/Aj4hrO3odPj1uADwPPLbUDpCZbj1iH\\/ifkX4yDxnLgS0QTYhssAJaSv9+MdsfdxLnUBi8Evsjafvqmx2riHvgG4hsnqdM2Bj5I9IdlX3wzifOA42h2F9LvkL+fjG7E22muWcBriKWts\\/fTTOIW4L3ARsPdPVK+jYFTiC+Ksy+0YcblNHdJ3SvJ3z9GN+JymmcWkYRfQv7+GWbcD\\/wXYtEuqdXmEm+id5N\\/YZWM84BDhrTPhuFQ8veJ0a04iOY4CPgP8vdJybgdeBuu26GWOgy4gvwLqVasJvoftxzCvpupL5G\\/P4xuxT+RbyvgC8S1lr0\\/asVlNCv5kia1CfAP9OsiHRv3Am+d8V6cvk2JDxaz94PRrXiE3Mmk3ko0j2fvh4xYBZxKTOktNdaRRNNV9gXThPg28cZS21unWV\\/DmCreSH1bAt+aZn27FjcDR8xob0oFzCVmDWvimNvMWEL0x9d01pDqbhhPjzOp61BiIq7s392keJKYm8F1bNQIWxMfwWVfGE2Nk6a\\/a9fZFsQc5Nm\\/2ehmPA5sTj0nFfodXYjvE+sYaAbMomZmX2L8bZO+gG+an1Ys63iiNUYqYR4x9K6Wn1Usq22OJNYveX52RdrMBGD6TiAebjtmV6TBlhJTHNfyyoplqZ9eUbGs64lrSOPbHvgx8KrsirSVCcD0nAT8G05WMZULiOa6GuYRq5xJJR1NvVamNUQLoya2IfBN4C3ZFWkjE4B19z5inLlNzVOr2YR5KGvXLZdK2ZS649IvqFhWW80FPg+8O7sibWMCsG7eA3yCZs+B3yQ1+\\/+PrliW+u2YimX5HcBgZgH\\/E\\/j97Iq0iQnA4N4FfDK7Ei3zy4pl1R5uqP6qea5dUrGstpsFfAp4Z3ZF2sI32cG8Dvgq7q91cRewXaWy5gEP4DcZqmMF0RXwRKXylhDDjTWYVcBrgW9kV6TpbAGY2iHE\\/Ns+\\/NfNryqWtT8+\\/FXPBsB+Fcu7umJZXTAH+Arw4uyKNJ0JwOR2Jr4wXT+7Ii1U86b1ooplSQAHViyrZjLdFQuIKZR3yK5Ik5kATGx94HSasbpdG9VMAPapWJYEsHfFsmwBmJ4tga8D87Mr0lQmABP7W+AF2ZVoMRMAdVnNc84EYPpeCPxNdiWayn7t8f02cFp2JVpuMbF6V2mzgYeAhRXKkkY9Qsw7sbpCWYupO6NmF\\/0n4GvZlWgaE4Bn2g64AtgsuyIttobog3usQlk7ATdVKEd6uh2A2yqUMx9YiffrmbgX2IsYUaERdgE80+fx4T9T91Hn4Q\\/xdiRlqHXuPQY8WKmsrloEfDa7Ek1jAvBUbwJ+PbsSHXBPxbJMAJRlp4pl+eY6c8cSc7pohAnAWhsDH8uuREfcVbGsnSqWJY1VM\\/k0ARiOTxL3emECMNYpwLbZleiImjer7SuWJY31rIpl3V2xrC7bGvhgdiWawgQg7IYrSQ1TzZuV8zQoyxYVy7IFYHjei12HgAnAqFOI+eQ1HA9VLGtRxbKksWqeezWvqa5bH\\/hIdiWawAQA9iQWjtDw1BoBAHXfwqSxap57j1csqw9+C9g1uxLZTADgv+F+GLaaCcAmFcuSxtq0YlmPViyrD+YAH8iuRLa+P\\/h2BF6TXYkOqnmzWq9iWdJYNc+9lRXL6ouTiGdAb\\/U9Afh9IhPUcJkAqA9qLjJTs1WtL+YBv5tdiUx9TgA2BN6SXYmOqnmzMgFQlprnnl0AZbyFHq8W2OcE4Deo24fXJ09WLMsWHGWZW7GsJyqW1SdbACdkVyJLnxOAk7Ir0GE1M2pvjMpS88v89SuW1Tdvz65Alr4mANsDL8muRIctqFiWw6OUpea5t0HFsvrmJfR0Fti+JgAnYtNxSTVvViYAylLzWxcTgHJmA8dnVyJDXxOAV2dXoONqNlf6cZSy1Dz3araq9dFvZFcgQx8TgE2BQ7Ir0XE1b1b3VixLGmtZxbJMAMo6AtgsuxK19TEB+DWc9780EwD1Qc1zzwSgrLnA4dmVqK2PCcBh2RXogZr9lTXfwqSxap57fgNQ3hHZFajNBEAlbF2xrKUVy5LGqpkAbFOxrL4yAei4TYG9sivRA9tXLOvWimVJY91SsaxeDlOrbD9g4+xK1NS3BOD59O83Z9iuYlk3VSxLGqvmufesimX11Wx69oLYt4dhrw5uom2pd27dXKkc6elqJQBzqNut1mf7ZFegpr4lAHtnV6An5gFbVirrhkrlSE9XKwHYGicuq6VXz4i+JQDPy65Aj9T6DuBeYEmlsqRRtwP3VyqrZpda3+2RXYGa+pYAPDu7Aj1S86Z1WcWyJIDLK5ZlAlBPr54RfUoA5uCFVNMuFcuqeTOWoG7SuWvFsvpue2BWdiVq6VMCsDXOAFhTzQ8uf1mxLAnqJgB+vFzP+sDm2ZWopU8JwKLsCvRMzZvWBRXLkgDOr1iWCUBdvZlzoU8JQK8meGiAPal3ft0A3FWpLOkO6k0CNIeefZjWAAuzK1CLCYBKWQjsVLE8WwFUy3kVy3oOLgRUW2\\/2d58SgN4c1AapOab2xxXLUr\\/9pGJZvRqX3hC9eVb0KQFYk12BHqrZd\\/ndimWp32qea\\/b\\/12cC0EGrsyvQQwdULOsa4MaK5amfrqXu7JP7VyxL4dHsCtRiAqCSDqPuOXZWxbLUTzXf\\/mfj8uUZHs6uQC19SgB6c1AbZBF1v2D+94plqZ++XbGsfYglzFVXb54VfUoAHsiuQE8dUbGs7wNLK5anflkK\\/KhieTWvHa1lAtBBJgA5Dq9Y1pPAtyqWp345nTjHarH5P4cJQAfdgyMBMhxO3bm1v1axLPXLv1UsaxYmABnWAA9lV6KWPiUAK4Bl2ZXooW2ou5jJD6g3S5v64ybqjv9\\/HrBVxfIUbsdRAJ11U3YFeuqYimWtBr5QsTz1w+epO5Lo6Iplaa3rsitQkwmAaji+cnlfAFZVLlPd9STwxcplvqZyeQrXZFegpr4lAFdkV6CnjgC2qFje7cB3KpanbjsTuLNieVsAL65YntayBaDDLs2uQE\\/NAV5VucxPVi5P3fWJyuUdD8ytXKbCtdkVqKlvCcBl2RXosRMql\\/dD4OeVy1T3\\/IK6q\\/9B\\/WtFa\\/XqJbFvCcAt1G3K01q\\/BmxUucxPVS5P3fM\\/Kpe3EXBk5TIVrie6D3ujbwkA1B3Ko7XmE0lATacDV1cuU91xLfUnlnoVsH7lMhV+kF2B2vqYALhufJ7XVy5vFfDnlctUd\\/wpdWf+A3hd5fK0Vs1pnhuh5gxtTbEbvhVmeQLYAVhSsczZwCXEwirSoK4A9qXu2P9tgVvxA8AszwLuyK5ETX1sAbiGno31bJB5wJsrl7kaOKVymWq\\/D1F\\/CfG34sM\\/y7X07OEP\\/UwAwGVjM72d+ufdN4HvVS5T7XUudZf9hWiNfXPlMrXW6dkVyNDXBMAV4\\/LsDLw0odz34+yAmtoq4A8Syj0aeE5CuQr\\/kl2BDH1NAC7AboBMb0so81LgnxLKVbt8Brg8ody3J5SpcAlwZXYlMvQ1AQD4SnYFeuwE6k4NPOpPgLsTylU7LCG+\\/K9tK+C4hHIVvppdgSx9TgBOo\\/5HPgrzgZMTyr0PeE9CuWqHdwP3J5W7XkK5imfA17IroRzfAtYYKXEv9WcGHPXNAeto9CfOIMdGRGKa\\/fv7Gr3+ILzPLQDggjGZNgfekVT2ycCypLLVPEvJOxd\\/F9gsqWzBX2ZXQLkuIj8L7WvcQXQHZDh+wDoa3Y+s\\/vf5xNok2b+\\/r3Hu1Ieo2\\/reAgDwF9kV6LHtgDcllf0t4HNJZas5\\/p76Y\\/5HvZWY\\/U85PpZdgWx9nAr46WYRy8a+MLsiPXUjMT1z7TnXARYSQ0L3Tihb+S4FDgZWJpQ9hxiK7Nj\\/HL8EXkC0BPSWLQBxAvz37Er02M7kLYCyHHgN8EBS+cpzP\\/Ab5Dz8AU7Ch3+mP6PnD3891bnk90n1NW4FFkx9iIp5FTEDXPZ+MOrEKuAV5FlAnPPZ+6Gv8d2pD1E\\/zMmuQIP8gvgS2H1S3ybAo+Qt1XwtMR74yKTyVdeHgP+VXP6rE8vvs8eIjz7vza6ImudT5GenfY2Hyf8g6h\\/J3w9G2cieDnpr4CHy90Nf48NTHyL11cbAzeSfpH2Nz095hMqaB5xN\\/n4wysS55M+49wXy90Nf41biw19pQkcSzcHZJ2sfYxWw\\/9SHqKhNgAvJ3xfGcOM\\/iAQ\\/0774rUlWrAZePvUhkuBvyT9h+xrfGeD4lLYpsUJY9r4whhOXAYvI9x3y90Vfwxn\\/NLD1cYbArHjz1Ienim2IjwOz94cxs7hm5Fg2wVvI3x99jJ+T3\\/WjlnkuMT48++TtU3xhoCNTzzbE22P2fjGmF1cB2z\\/jqOb6Ivn7pU9xP7B4kAMjPd1r8HuAWnEpsMFgh6WqzYk3iOz9Y6xbXARsOc7xzLYQuJz8\\/dOHWA2cMNhhkcb3IfJP5K7HXcCOgx6QBJsC55G\\/n4zB4kfEx5xNtROwhPz91PX44IDHQ5rU35N\\/Mnc1VgAHDX4o0swHvkr+\\/jImj9PJnVVyUAcQU1Fn76+uxt8Nfiikyc0DziT\\/pO5aPEEsy9sWs4BTyN9vxvhxKu1a3+Q1xDWQvd+6Fv9Ku84DtcB6OIxnmPEk8Pp1OgLN8Uai5SJ7HxoRy4E3THrEmutE4lrI3oddiXOJ1jpp6DbARYOGEatoznC\\/6doXuIH8fdn3uA7YZ4pj1XRvw4+NhxHnABut476X1sl84Ovkn+xtjSeAN63zXm+mzYF\\/J3+f9jW+QXyg2QWvBx4nf5+2Nb5BzN8iFTcH+Az5J33bYjnw69PY3033RuAR8vdvX2IF8J6Bjky7vBI\\/DJxO\\/C32+auyWcCf4dzeg8YdwIumtafbYXdiSens\\/dz1+A9gtwGPSRsdBNxJ\\/n5uQ6wG\\/mR6u1kajlcQs01lXwxNjouAHaa7g1tkLvFm+jD5+7xrsQL4ANH61nXbE4lO9j5vctyHk\\/yoIXYnZrLLviiaGP9I\\/\\/rmdsZlhYcZ\\/5f+Tee6PvA58vd9E+MCmj1xmHpoPvBx7BIYjQeA\\/zSjPdp+LyPmo88+Fm2Na4lhcn12AnAv+ceiCbGamOvBhX3UWMcAN5F\\/sWTG\\/wGePdMd2RHzgT8mmiyzj0tb4l7gD\\/FGP2oH4LvkH5fMuBl4+Qz3o1TFAmLGuMfIv3BqxhLii3g900ZEH7bfi0wcDxOtaF0Z2jdsJwJ3k3+casbjxFv\\/hkPYf1JV+wLfI\\/8iqnGRfgrYbDi7rdMWAR\\/BZt2xsQz4MDGvgia3iBj21oc5A84EnjOc3SbleTnd\\/EhwNfC\\/gV2Gt6t6Y32iteRq8o9jVtxIjJpYOMN92Ue7EpPfdHEGwQuBVw1vV0n5ZgHHEl+wZl9gM41VwBnAC4e6h\\/ppDjEBzDfpx8IwjxMPrlfQjyF9pe0DfJlurCdwHnGPnDXUPSQ1zFHETbBtN\\/wHgU8Tbx8avm2J7wQuIf9YDzsuBt4PbDO0vaWxdieWLX+Q\\/GO9LrGKmE774OHvEqnZtgf+K7GgSfaFOFGsJlot3o4f4tS0KzHT5MW0s5l39Ujd\\/xS7iGraEHgn8DPyz4HJ4mJipMf2ZXaD1C4vIL6Avob8i3MVMRPZ+4GdCv5mDWZb4D8DXwPuIf\\/8mCjuJtZifzO+6TfBYqJF6Rc0Y36Sa4CPAnuU\\/NEanH0tzbQT8GvERDIHAc8qXN4aohXip8BZxLKaywqXqenbHXgxcCjwfOKGWnvM\\/OPEJEcXE323PyVu8GqmLYn7yTHEubML5e\\/\\/1wM\\/HIkfEGscqEFMANphO+KDu72IhVB2I6ab3WIdt7OGGKt\\/HTHL2rXAL4k3hAeGVVlVN4\\/oMtibuLEvJpLIxcSb+HSnYl5JnC83E5Nb3UycM1cQD\\/snp19lJduUWJxrP+Lc2WXkz61Z9+fCbay9p1xHnBuXArcPq7IqwwSg3eYBWxHZ\\/WbEV9UbjvzvK4FHib7YpWNiVUpNlWkhMX58EXGezCYWLtpo5P9\\/mHiYryISwXtHYnn1mirbHOJ+MhqziQRyAbHk9fKRuH\\/knx8m7jWSJEmSJEmSJEmSJEmSJEmSJEmSJEmSJEmSJEmSJEmalFMBN9881s7vvh2xfOZ2I7E1MUXnAmKK1wWsnfd9ObFgCyN\\/**************************\\/NsDXwbGCHkXg2MZ3v5mP+3IC1U\\/oupPyiQI+zdorg0Wlgx55X9xFzw986ErcR6wmoOeYTa0XsPBI7EtP+jj2vFhH3HohzauHI31eOxANj\\/n43sdDPHSNxF3FPuR54ovSP0fSZADTLNsDBwD7AnsQqb7uy9kIsaTVxs76cWOHtYuAS4iausrYhVvXbizjmewLPI9Z16ILlwK+AK4kVBK8kzq+7MivVEzsS59YLRmJvYnXR2RXKfoJYIGj0mF8KXEAkDGoAE4BcewKHEw\\/9Q4hsvGmWEcu9njsSV+ZWp\\/XmEauwHQwcOPL3HVJrlOc2YiXKnxMPhp9jK9RMzCISyKOAI4nlohel1mh8NxDLR18A\\/BjvKWlMAOragLgwXwm8gnbe+JcQa3ufCXwHeDC3Oo03i1hydfSmfBjdebMftuWsTTa\\/T7QSrEmtUfNtAryKuKccSXQbtc0twP8h7ic\\/AFbkVkcang2A1wFnECf2mg7Fo8RF+1Zgi2HtsA6YS7x9nUp0oWQfp7bGPcCXgRNZ2wet6K9\\/G\\/HQfIz84zTMWAF8m7hnLhjWDpNqmgMcA3wJeIj8i6pGPEFcuMcRD8C+mQO8HDiN\\/hzzmvEI8M\\/Em26Nb2KaZi5wPNHy9iT5x6NGPAR8ETiauL6kRtsC+BOibzP74smMO4CPAs+Z2e5shf2ATxEftGXv977EPcDfAgcMcHza7rnAx4iv7LP3e2bcStxbbWlU4+wHfIbow8y+UJoUq4muj0Omv2sbaT7RLP098vdx3+NC4B1077uK\\/Ynuj7687Q8ajwL\\/Bhw0\\/V0rDcdhwDnkXxRtiJ8Ax9LuD093AD4B3E\\/+\\/jSeGg8Q31wsnvDoNd9sogvtPPL3ZxviHOIeLFV1KPGVcvYF0Ma4nLjJtcnexNvY4+TvP2PyWEW0Oh047pFsrlcDV5C\\/\\/9oY5xD3ZKmoA\\/CNf1jxE+DF67b7qzuAGOWwmvz9Zax7fJfmJwKH4Bv\\/sOJsoutEGqqtgX8i3i6yT\\/KuxTeJscxNsifRz+iDvxvxPZr3YNiEOPez903XYhXwedo5H4IaZh7wHqJ\\/MfvE7mp8fOCjUd6zga9iotfFWA38KzEHflN8gPz90tV4BDiF+GBXWmcvJuYvzz6RuxqrgfcNfDTK2oC4GT9M\\/n4xysZjxMeCG9MMf4gtTSXjKmLabWkgC4i3UofflIvVwO8NekAK+y2ct6GPcSdwEs0YlfJObHUqGauIYdrOJqlJHQJcQ\\/4J2+VYDbxl0ANS0GJiKtXs\\/WHkxo+A3cn3dmwJKB1XY2uAxjEH+DBm4TXi5AGPSSlzgT\\/CSZuMtbEC+C\\/kTzH8++Tvi67Hk8S3AU4tLCAW1jiL\\/BOzD\\/HBAY9JKYuJ5Uez94PRzLgU2Idc7yV\\/P\\/QhfgBsO+AxUUcdTsxbn30y9iE+POAxKeWN+JGfMXWsJD4InU2ej45TL2P4cTfwsgGPiTrmffihX634+wGPSQlbECuoZe8Do11xNrljyf9xgnoZw40niFYX9cQc4NPkn3h9iTPJWx74AODGAepoGONF5hviHOBbA9TRGE58jvxvQFTYRvg2WDMuJG\\/ozTuIMd\\/Z+8Bod4x+NJbRJbAB8LN1qKsxszib5s1KqiHZEbiS\\/JOsL3EjOU2oC4B\\/mUZ9DWOy+N\\/kJLPbAjevY12N6cdlwLMGOTBqj+fiRVQzHgL2GOTADNm2wM+nUV\\/DGCQuJWcq4b3xA9aacSPtXlZaY+wO3E7+SdWXWA2cONCRGa798Tgb5WMJOZPJnIATBdWMO4lFwdRiexAHMvtk6lNkDPc7imh1yP7tRj\\/iUeA3qe8vpllfY3qxhPx5ITRNewH3kn8S9SnOpP7HUr8NPD6EuhvGusSTxBz+Nc3G6atrx1JyujM1A4txgp\\/acROw6SAHZ4j+CJtFjdz4M+raDL9nqh23ER+RqwW2JBZ9yD5p+hRPEMsn1+Ra6kZT4q+o60XY6lU7riN3YigNYGPgIvJPlr7FBwY5OEN0ypDqbRjDik9Q14eGWHdjsLiU+q2cGtBc4HvknyR9i7Oo2+\\/\\/4UK\\/wzBmGn8DzKKO2cA5FX6T8dT4Lq4k2EifJP\\/k6FsspW6z2H8r9DsMY1jxF9SzHX7onBF\\/PcjBUT0nkX9S9DFeO8jBGZLfLfg7DGOY8X7qeUPB32FMHG8d5OCovIOIcbnZJ0Tf4luDHJwheQOwqtDvMIxhx2rgbdTz9UK\\/w5g4VgIHDnJwmqxWf1Upi4Bf4tzNtS0l5lm4p0JZxxDzC7hSl9pkFXA8ce6WtjWxzsmiCmVprduBfYH7sisyXRkrXA3LLOCf8OGf4d3Uefg\\/D\\/gaPvzVPnOIRan2q1DW3cDvVShHT\\/Us4PPZleirk8lvBupjnDXIwRmCRcTY2+zfaxgziTuo95LiLIE5UXtGyKFpaxfAnsAviKVfVc9jxNzY1xYuZz3gXOCQwuVINVwIHE70G5f0XOByYP3C5eipHiUmZ7o8uyLrqo1dAPOIpjUf\\/vV9jPIPf4BT8eGv7jgA+FyFcq6n\\/qyEioTry8RcNCrsT8lv8uljXEedNwuHNRldjXdQ3gLghqTf1\\/eoPSPqjLWtC2BXYjpGm7jqOw44o3AZ+wAXABsULkfK8BhwGNF9WdIJwDcKl6FnWkncw67Prsig2pQAzCKm+j0quyI99CPgJYXL2BC4GNilcDlSppuIkQEPFS7nx0Syobp+CBxJtAg0Xpu+AXgLPvwzrCGW3S3tU\\/jwV\\/ctBj5doZz305KHUMe8BHhjdiW6ZkNWgACGAAAThElEQVTgLvL7ePoYXxng+MzU8Ym\\/zzAy4nWU9y+Jv6\\/PsQTYaIDjowF9hPyD2sd4FNhp6sMzI9sBy5J\\/p2HUjvuBHShrMfHdQfZv7WOcMvXhydeGLoDtgfdmV6KnvgDcXLiMv8MpTNU\\/mwL\\/i7LfYd0EfKng9jWxP8ZZaofii+Rnc32Mxyn\\/9v9bDfidhpEZpfuLd8BWgKyoMffDjDR9FMCewGW0o6Wia\\/4ReFfB7W8OXEUsZCL11X3AHsR8\\/qV8jrqrEyqsIhZNuzq7IhNp+oP1gzS\\/jl30BPCXhcv4a3z4S5sDnyxcxkeIFj3VNYcWTg7UFDsTD6LsZpw+RukVrg4gsuPs32kYTYmXUNYXG\\/Ab+xg1ulKnrclv13+McytnWAP8TcHtzyY+\\/GvyuSfV9mnK3u\\/+mri2Vdc8Yk6GRmrqTXg74M3Zleips4i++VLeRKycJWmtPYG3F9z+FcD3C25fE3sLsG12JcbT1ATgXcD87Er01CcKbnsh8NGC25fa7M+J4YGllGzZ08TWB96ZXYnxNDEBmEtkTKqv9FvCe2loJiw1wCLgDwtu\\/7uUbd3TxN5BdAc0ShMTgBOILgDV92nK9RNuBryv0Lalrngv5UbHrAH+odC2NbltgVdmV+LpmpgA\\/E52BXpqOTF3eCl\\/QiQBkia2kLhWSjkNWFFw+5pY47oBmpYA7Aq8NLsSPfWvlFuidGvg5ELblrrmdyg3jeyDwOmFtq3JHUMMb2+MpiUAv03zZyfsqs8W3PYfABsU3L7UJfMp213W+ClqO2o28IbsSozVtIftr4DdsyvRQ5cB+xba9sbALZT9ulnqmuXEBDLLCm3\\/SmIKYtV1BbB3diVGNakFYD98+Gc5reC2T8aHv7SuFgLvLrj9kte8JrYXMedDIzQpAXhtdgV6ag3l+gTnA+8ptG2p695Nua6zf8aZAbM05lnXpATgxOwK9NT5RBN9Ca\\/FBX+k6VoEvK7Qtm8FflZo25rcb2VXYFRTEoDdgOdmV6KnSg7988t\\/aWZKdgOUvPY1sd2A52RXApqTABydXYGeWgV8vdC2nw8cWGjbUl\\/sBxxcaNunE\\/cA1XdMdgWgOQlAI3ZGD\\/0UuKfQtn37l4bjXYW2uwT4eaFta3KNeOltQgIwFzgiuxI9dUah7W5Agz50kVruN4FNCm271D1AkzuKBqwN0IQE4GBirLjqO7PQdo8HNiq0balvFgCvKbTtUvcATW5j4IXZlWhCAnBYdgV66gZi4qUSTiq0XamvSl1TVwA3Fdq2JndodgWakAAckF2BnirV9LcV8LJC25b66iXEzIAlfKfQdjU5WwCAF2VXoKfOKbTd1xLfdUganlnEtwAllLoXaHLpz77sBGAbYPvkOvTRk8BPCm37hELblfru+ELb\\/REOB8ywA7BtZgWyE4D0JpCeupgyS\\/9uit90SKUcTLw0DdsDwKUFtqup7Z9ZeHYC8Pzk8vvqB4W2exwNGNoiddRs4NhC2z630HY1uRdkFp6dAOyTXH5f\\/bDQdkvdnCSF4wptt9RLgSaX+gyclVk4cC2wS3Id+mYNscjI\\/UPe7hxiVsHNh7xdSWs9TFy\\/Twx5u5sDy8h\\/JvTNtcTaACkyWwA2oCELIvTMdQz\\/4Q\\/RlOXDXyprI8qssXEfcGOB7WpyzwUWZhWemQDsmVx+X\\/2i0HYd+y\\/VUepaK3Vv0MRmA8\\/LLDyL\\/f85Sl3kRxXarqSnMgHolrRnYWYCsFdi2X12YYFtzsGlf6VaDgDmF9iuCUCOvbMKtgWgX9YAlxfY7j7AhgW2K+mZ5lNmCPWlxD1CdfUyAUj70T12O2UmAPLtX6qrxDX3EHBnge1qcvtmFZyVAGwLbJlUdp9dVWi7JgBSXaWuuVL3CE1sC8rM8DilrATAt\\/8cpS5uV3SU6io1jboJQI6UZ6IJQL\\/8qsA25wG7FtiupIntTJnx4yXuEZparxIARwDkuKbANncD1iuwXUkTmw3sUWC7JgA5epUApH300HPXFdjmngW2KWlqJV6kri+wTU0tZVRcRgIwl8SZj3rsEWBJge2aAEg5Slx7dxHrDaiuPYlnY1UZCcAuwPoJ5fbd9ZQZ47t7gW1KmlqJa28NrgmQYT6xLkBVGQmAEwDluLbQdl3NUcpR6torda\\/Q5Ko\\/GzMSAEcA5CjR\\/w+u6ChlWUyMwhm2UvcKTa76s9EEoD9KXNTbEsuTSqpvHrBDge2aAOToRQJgF0COEhd19T4rSU9RohvABCBH57sANgJ2rFymQomL2v5\\/KVeJJNwEIMdOwMY1C6ydAOwNzKpcpuBBYGmB7doCIOUqkYTfQ9wzVNcsKg+rzkgAVF+pjN4WAClXqWvQCYFyVH1GmgD0Q6kEwBYAKVepa9BugBydTgD8ADCHQwClbnIoYLdUfUbWTgBcBCiHQwClbppLmQ+rTQBy7EPF7+RqJgDbA5tVLE9rlejPs\\/lfagaHAnbHpsTLVRU1EwCbi\\/OUmNrTDwClZjAB6JbFtQqqmQBsWbEsrXXfSAybLQBSM5S4Fu+lzH1DU9uqVkE1E4ASH6poaqWG89gCIDWDQwG7pdqywDUTgIcqlqW1XAVQ6rZS16LdADmqTcJUMwG4o2JZWqtUFr9zoe1KWjc7AesV2K4JQI5qz8qaCcCvgMcrlqfgEECp2+bgUMCueIxyrbbPUDMBeBy4oGJ5Co4AkLrPkQDdcD7wRK3Cak8E9K3K5ck5AKQ+MAHohn+vWVjtBOArwKOVy+yzpcADBbZrC4DULCWS8gcos4qoxreSeEZWUzsBWAZ8pnKZfeYiQFI\\/OBSw\\/f6BynMv1E4AAD5CJAIqzzkApH5wKGC7LQU+VrvQjARgGXByQrl9VOLinYVDAKWm2RGHArbZySS8GGckAAD\\/BpyaVHaflBgB4BBAqXnmUGYOeROA8j4JnJ5RcFYCAPBHwI8Sy++DEhev\\/f9SM5W4Nk0Ayjof+EBW4ZkJwJPAbwJXJdah624osE37\\/6VmKnFt+hFgOVcAr6biuP+ny0wAIPo8jqHMg6rvllBm\\/QVbAKRmKpEAPETcSzRc1xHPvnszK5GdAEDMe3wocFl2RTqmVNOdLQBSM5VKzm0FGK6rgJcCd2VXpAkJAESG+VLg59kV6ZBSF60tAFIzORSw+S4gXngbsTheUxIAiAkQjgC+lF2RjigxAmAW8JwC25U0czsC8wts1wRgOP4FOAq4P7sio5qUAECshPRm4A+AVblVab1SqwBuWGC7kmZuNg4FbKJVwAeB1xPT\\/TZG0xKAUacChwE3ZlekxUpctPb\\/S83mokDNcivx1v+X2RUZT1MTAIi+khcAX82uSAutoczICvv\\/pWYrNRRwTYHtdt3XgefT4PlumpwAADwI\\/PZI3JNclza5E1heYLsmAFKzlbhGlxP3FA3mbqK5\\/0QqL+6zrpqeAIz6KrAb8D\\/x24BBOARQ6idXBcyzBjgN2JP44K\\/x2pIAQKxN\\/R7i24BLkuvSdK4CKPWTQwFzXAQcDLyR5Ml91kWbEoBRFwD7A6\\/Fk3IiDgGU+unZwPoFtuu9dnw3A+8EXkQL57FpYwIA0dRyOtHU8k4aMqlCg5QaAriwwHYlDY9DAeu4gxiuvjvwWWB1bnWmp60JwKgniJ2\\/C\\/Bu7KcaVWI\\/2PwvtYNDAcu5DjiZ2MenEnPXtFbbE4BRK4G\\/Iz4UPA44J7c6qVbjKoBSn5W4Vm+gpW+5Q3IR8CbgecDf07AJfaarKwnAqNXAGcDRRJ\\/MacCK1BrVdztlTk6HAErtUOJaXUncW\\/pkOfBl4IXAASN\\/79QotK4lAGP9gvgicwvig8Fz6MdkFqWa6kwApHZwKODMXER8W7Yt8dZ\\/YW51yulyAjBqJfHB4NHArsCf0+0phh0CKPWbQwHX3Q3Ah4kXnQOIb8seTq1RBXOzK1DZ9cB\\/HYmdgWOJ2ZoOpjvJkEMApX57FrCA4XcFdi0BuIp4OTyDeOvvnb4lAGPdSHzFeSrR1HPsSBwJbJBYr5kq0QKwHQ4BlNpiNvGCc+WQt9v2BGAF8H3igX8GsCS3Ovn6nACMdRfR5PNZYp\\/sCxwKHAK8DNgsr2rrrEQLgP3\\/Urs8FxOAR4CfAecD5wE\\/oeXD9obNBOCZniSagy4iWgfmAPsRUxAfQaxQuENa7Sa3CripwHbt\\/5fapcQ1eyNxj5lTYNvDcCtx3\\/7xSFxKx77aHzYTgKmtYm1C8KmR\\/20TYG9gD2I2wv2JZR+zuw5upUyGawuA1C4lEoDHgNuAnQpse108QbRGXES0clxFTMPrirHryARgeh4kmpTOG\\/O\\/zSUmItqTmIpzbOwIrFehXq4CKAnKJe3XUicBeBy4hWjRHBtXAtcQLbWaIROA4XmSODnH63ebDWzPU5OCLUdii5FYNPLnTBKFUkMAbQGQ2qXkXADHzOC\\/f4xYLe9eYBmwdMyfYx\\/0d9DvmQerMAGoYzXRdHYb0Tc1mY2IRGA0OVg4EusRq3wtII7bRiP\\/\\/mZj\\/ruzh1rr4BBAqX1KDQU8m7jnjI6Rv3\\/kz4eI7tIVxEP+cWImvUeIh\\/3og77zY+vbZFZ2BdR429O\\/KUClLtgbuCK7Emqurkx+o3Ls\\/5fayWtXkzIB0FTs\\/5fayWtXkzIB0FS8iUjtZAuAJmUCoKl4E5HayWtXkzIB0FS8iUjtZOudJuUoAE1mFjFsx4WApPZZQwwPXp5dETWTLQCajKsASu01i1gVUBqXCYAmY\\/O\\/1G5ew5qQCYAm481DajevYU3IBECT8SMiqd28hjUhEwBNxpuH1G62AGhCJgCajDcPqd28hjUhhwFqIg4BlNrPoYCakC0Amsj2+PCX2s7lvDUhEwBNxP5\\/qRvsBtC4TAA0EW8aUjd4LWtcJgCaiC0AUjd4LWtcJgCaiG8NUjd4LWtcJgCaiG8NUjeYAGhcDgPUeBwCKHXLxsQ1Lf1\\/tgBoPA4BlLrFoYB6BhMAjccmQ6lb7NLTM5gAaDzeLKRuManXM5gAaDw7ZFdA0lB5TesZTAA0ni2zKyBpqLbIroCaxwRA45mXXQFJQzU3uwJqHhMAjceVw6RuWZFdATWPCYDGc0d2BSQN1a3ZFVDzmABoPL\\/KroCkobo6uwJqHmcC1Hg2BZYBc7IrImkodgZuyq6EmsUWAI3nAeD87EpIGoor8eGvcZgAaCKfz66ApKHwWta47ALQROYD1wA7ZldE0rQtI2b2fDC7ImoeWwA0kceAD2VXQtKMfAQf\\/pqALQCayreBY7MrIWmdnQ8cAazKroiayQRAU9kSuACXE5Xa5C7gIBz\\/r0nYBaCpLAVeDtyZXRFJA7kPeCU+\\/DUFEwAN4nrghcAl2RWRNKmbgEPxWtUATAA0qDuBw4HvZFdE0rj+AzgYZ\\/LUgJzpTeviceBrxMIiR+D5IzXFacCJxCRe0kD8CFDTdTjwRWBxcj2kPlsCvB04M7siah\\/f4DRdtwCfJdYZPxi7k6TaTgdeBfwyuyJqJ1sANAz7A58mhh1JKusq4PeAc7MrIkmjjiVGDKwxDGPosQx4D9HqJs2YXQAapmuBzxAfIu0HLMytjtQJDwN\\/BbwW+DGwOrc6kjS5+cA7gNvIf3MyjDbGg8DHgc2RpBaaD\\/wOdg0YxqBxF\\/BBYCOkgvwIULXMBo4kWgVeg91P0tNdRIysOQ1YmVwX9YAJgDI8hxi7\\/FZgi+S6SJkeJYbz\\/Q0O51NlJgDKNB84BjgJeDWwXm51pCpWEytsng58Bbg3tzrqKxMANcWmwHFEMnAUnpvqnquIh\\/6XiEV7pFTeZNVEOxPfCRwLHILfC6id1hCr8p0BfAO4LLc60lOZAKjpNidaBI4lWgg2ya2ONKlHgfOIufm\\/QQyDlRrJBEBtMp9YhfDXgZcA++AaBMp3DfBD4CzgbGB5am2kAZkAqM02Ag4EXjYSz8eEQOXdRbzln0M89G\\/JrY40PSYA6pItiRaCw4EXAfsC66fWSG33JPHx3i+IaXh\\/gM366ggTAHXZXGA3YrXC0TiA6EqQxnMXMSHPecD5wMXAitQaSYWYAKhvFhALFR0A7A08D9gD51vvm0eAq4m3+yuAC4kH\\/0OZlZJqMgGQwmbAnkQysPOYvy\\/G66TNHiTWobgKuBK4ceTvv8JV9dRz3tikyW1GdCPsBOw4EjuM\\/PNOuORxtkeBW4kP8Ub\\/vAW4mVieeklazaSGMwGQZmYLIiEYTQ52ArYDtiI+Stxi5E+vtXV3P3A3sAxYCtzBUx\\/ytxJ99pKmwZuSVN5snpoMbE0kCKP\\/vA0xpHEzokVhIbAhMelRF4Y1PkiMjV8x8veHR\\/5cSjzglxIP+Xt46gP\\/iYzKSn1hAiA12wIiIdh4JBYCGxBrJ2wIzBvz59j\\/Zuzwx9F\\/Z9Rsnjmj4sPEkLdRj\\/LUJWmfHPl3xrqf6EcffaivIB7097P2gf\\/0\\/0aSJEmSJEmSJEmSJEmSJEmSJEmSJEmSJEmSJEmSJEmSJEmSJEmSJEmSJEmSJEmSJEmSJEmSJEmSJEmSJEmSJEmSJEmSJEmSJEmSJEmSJEmSJEmSJEmSJEmSJEmSJEmSJEmSJEmSJEmSJEmSJEmSJEmSJEmSJEmSJEmSJEmSJEmSJEmSJEmSJEmSJEmSJEmSJEmSJEmSJEmSJEmSJEmSJEmSJEmSJEmSJEmSJEmSJEmSJEmSJEmSJEmSJEmSJEmSJEmSJEmSJEmSJEmSJEmSJDXB\\/wMHFBVAmEvobQAAAABJRU5ErkJggg==\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.184097, "xdebug_link": null, "collector": "log"}, {"message": "[17:04:47] LOG.info: POC from input: {\n    \"poc\": \"Saiful Islam\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.184177, "xdebug_link": null, "collector": "log"}, {"message": "[17:04:47] LOG.info: Manager from input: {\n    \"manager\": \"Saiful Islam\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.184253, "xdebug_link": null, "collector": "log"}, {"message": "[17:04:47] LOG.info: Team Lead from input: {\n    \"team_lead\": \"<PERSON><PERSON>\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.184316, "xdebug_link": null, "collector": "log"}, {"message": "[17:04:47] LOG.info: Launch from input: {\n    \"launch\": \"2025-08-19\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.184376, "xdebug_link": null, "collector": "log"}, {"message": "[17:04:47] LOG.info: Department ID from input: {\n    \"department_id\": 8\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.184436, "xdebug_link": null, "collector": "log"}, {"message": "[17:04:47] LOG.info: Decoded Logo Path: {\n    \"logo_path\": \"images\\/logo-68a45a4f2d0df.png\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.185241, "xdebug_link": null, "collector": "log"}, {"message": "[17:04:47] LOG.info: Decoded Icon Path: {\n    \"icon_path\": \"images\\/icon-68a45a4f2d281.png\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.185329, "xdebug_link": null, "collector": "log"}, {"message": "[17:04:47] LOG.info: Existing Team Data: {\n    \"team\": {\n        \"id\": 43,\n        \"name\": \"<PERSON>G<PERSON>U<PERSON>\",\n        \"icon\": \"images\\/BIPlfm6QqsGAjkgIRq6QuE796wZDzb7ea6ENmucd.png\",\n        \"logo\": \"images\\/3JMnysc6PaO87pK0YS8lUa8SHQmKbTk8r3cAzDdz.png\",\n        \"poc\": \"Saiful Islam\",\n        \"manager\": \"Sha<PERSON><PERSON><PERSON>l <PERSON>\",\n        \"team_lead\": \"<PERSON><PERSON>\",\n        \"workday\": [\n            \"Tuesday\",\n            \"Wednesday\",\n            \"Thursday\"\n        ],\n        \"billable_hours\": 20,\n        \"launch\": \"2025-08-18\",\n        \"created_by\": \"150\",\n        \"updated_by\": null,\n        \"created_at\": \"2025-08-18T12:50:21.000000Z\",\n        \"updated_at\": \"2025-08-18T12:50:21.000000Z\"\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.198662, "xdebug_link": null, "collector": "log"}, {"message": "[17:04:47] LOG.info: Updated Team: {\n    \"team\": {\n        \"id\": 43,\n        \"name\": \"<PERSON>G<PERSON>U<PERSON>\",\n        \"icon\": \"images\\/icon-68a45a4f2d281.png\",\n        \"logo\": \"images\\/logo-68a45a4f2d0df.png\",\n        \"poc\": \"Saiful Islam\",\n        \"manager\": \"Saiful Islam\",\n        \"team_lead\": \"<PERSON><PERSON>\",\n        \"workday\": [\n            \"Tuesday\",\n            \"Wednesday\",\n            \"Thursday\",\n            \"Friday\",\n            \"Monday\"\n        ],\n        \"billable_hours\": 20,\n        \"launch\": \"2025-08-19\",\n        \"created_by\": \"150\",\n        \"updated_by\": 150,\n        \"created_at\": \"2025-08-18T12:50:21.000000Z\",\n        \"updated_at\": \"2025-08-19T11:04:47.000000Z\"\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.201599, "xdebug_link": null, "collector": "log"}, {"message": "[17:04:47] LOG.info: Team Attached to Department: {\n    \"department_id\": 8,\n    \"team_id\": 43\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.20715, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755601486.735306, "end": **********.211863, "duration": 0.47655701637268066, "duration_str": "477ms", "measures": [{"label": "Booting", "start": 1755601486.735306, "relative_start": 0, "end": **********.144668, "relative_end": **********.144668, "duration": 0.4093620777130127, "duration_str": "409ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.144675, "relative_start": 0.40936899185180664, "end": **********.211866, "relative_end": 2.86102294921875e-06, "duration": 0.06719088554382324, "duration_str": "67.19ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 24403384, "peak_usage_str": "23MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "PUT api/teams/{id}", "middleware": "api, auth:sanctum, cors, verified, role:super-admin|admin", "controller": "App\\Http\\Controllers\\TeamController@updateTeam", "namespace": null, "prefix": "api", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fapp%2FHttp%2FControllers%2FTeamController.php&line=330\" onclick=\"\">app/Http/Controllers/TeamController.php:330-452</a>"}, "queries": {"nb_statements": 13, "nb_visible_statements": 14, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.0078, "accumulated_duration_str": "7.8ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/sanctum/src/PersonalAccessToken.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php", "line": 66}, {"index": 14, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 67}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 42}], "start": **********.162039, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "PersonalAccessToken.php:66", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/sanctum/src/PersonalAccessToken.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php", "line": 66}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FPersonalAccessToken.php&line=66", "ajax": false, "filename": "PersonalAccessToken.php", "line": "66"}, "connection": "creative_app3", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `personal_access_tokens` where `personal_access_tokens`.`id` = '444' limit 1", "type": "query", "params": [], "bindings": ["444"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/laravel/sanctum/src/PersonalAccessToken.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php", "line": 66}, {"index": 20, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 67}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 24, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 42}], "start": **********.166026, "duration": 0.00209, "duration_str": "2.09ms", "memory": 0, "memory_str": null, "filename": "PersonalAccessToken.php:66", "source": {"index": 19, "namespace": null, "name": "vendor/laravel/sanctum/src/PersonalAccessToken.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php", "line": 66}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FPersonalAccessToken.php&line=66", "ajax": false, "filename": "PersonalAccessToken.php", "line": "66"}, "connection": "creative_app3", "explain": null, "start_percent": 0, "width_percent": 26.795}, {"sql": "select * from `users` where `users`.`id` = 150 limit 1", "type": "query", "params": [], "bindings": [150], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 161}, {"index": 21, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 69}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}, {"index": 26, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 42}], "start": **********.1723459, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "Guard.php:161", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 161}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FGuard.php&line=161", "ajax": false, "filename": "Guard.php", "line": "161"}, "connection": "creative_app3", "explain": null, "start_percent": 26.795, "width_percent": 4.615}, {"sql": "update `personal_access_tokens` set `last_used_at` = '2025-08-19 17:04:47', `personal_access_tokens`.`updated_at` = '2025-08-19 17:04:47' where `id` = 444", "type": "query", "params": [], "bindings": ["2025-08-19 17:04:47", "2025-08-19 17:04:47", 444], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 83}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 42}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}], "start": **********.174371, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "Guard.php:83", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 83}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FGuard.php&line=83", "ajax": false, "filename": "Guard.php", "line": "83"}, "connection": "creative_app3", "explain": null, "start_percent": 31.41, "width_percent": 14.231}, {"sql": "select `name` from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `role_user`.`user_id` = 150", "type": "query", "params": [], "bindings": [150], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": "middleware", "name": "role", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Middleware\\RoleMiddleware.php", "line": 18}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 18, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 30}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 20, "namespace": "middleware", "name": "cors", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Middleware\\Cors.php", "line": 20}], "start": **********.1804922, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "role:18", "source": {"index": 16, "namespace": "middleware", "name": "role", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Middleware\\RoleMiddleware.php", "line": 18}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fapp%2FHttp%2FMiddleware%2FRoleMiddleware.php&line=18", "ajax": false, "filename": "RoleMiddleware.php", "line": "18"}, "connection": "creative_app3", "explain": null, "start_percent": 45.641, "width_percent": 3.462}, {"sql": "select exists(select * from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `role_user`.`user_id` = 150 and `name` in ('super-admin', 'admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead', 'team-member', 'guest')) as `exists`", "type": "query", "params": [], "bindings": [150, "super-admin", "admin", "hod", "manager", "team-lead", "coordinator", "shift-lead", "team-member", "guest"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": "middleware", "name": "role", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Middleware\\RoleMiddleware.php", "line": 21}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 15, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 30}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 17, "namespace": "middleware", "name": "cors", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Middleware\\Cors.php", "line": 20}], "start": **********.182283, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "role:21", "source": {"index": 13, "namespace": "middleware", "name": "role", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Middleware\\RoleMiddleware.php", "line": 21}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fapp%2FHttp%2FMiddleware%2FRoleMiddleware.php&line=21", "ajax": false, "filename": "RoleMiddleware.php", "line": "21"}, "connection": "creative_app3", "explain": null, "start_percent": 49.103, "width_percent": 3.077}, {"sql": "select count(*) as aggregate from `teams` where `name` = 'NeverGiveUp' and `id` <> '43'", "type": "query", "params": [], "bindings": ["NeverGiveUp", "43"], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 928}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 616}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 422}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 453}], "start": **********.1924582, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "creative_app3", "explain": null, "start_percent": 52.179, "width_percent": 4.103}, {"sql": "select count(*) as aggregate from `departments` where `id` = 8", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 882}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 854}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 616}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 422}], "start": **********.1943, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "creative_app3", "explain": null, "start_percent": 56.282, "width_percent": 2.308}, {"sql": "select exists(select * from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `role_user`.`user_id` = 150 and `name` in ('super-admin', 'admin')) as `exists`", "type": "query", "params": [], "bindings": [150, "super-admin", "admin"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/TeamController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\TeamController.php", "line": 397}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.195641, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "TeamController.php:397", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/TeamController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\TeamController.php", "line": 397}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fapp%2FHttp%2FControllers%2FTeamController.php&line=397", "ajax": false, "filename": "TeamController.php", "line": "397"}, "connection": "creative_app3", "explain": null, "start_percent": 58.59, "width_percent": 2.949}, {"sql": "select * from `teams` where `teams`.`id` = '43' limit 1", "type": "query", "params": [], "bindings": ["43"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/TeamController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\TeamController.php", "line": 403}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.197183, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "TeamController.php:403", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/TeamController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\TeamController.php", "line": 403}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fapp%2FHttp%2FControllers%2FTeamController.php&line=403", "ajax": false, "filename": "TeamController.php", "line": "403"}, "connection": "creative_app3", "explain": null, "start_percent": 61.538, "width_percent": 2.436}, {"sql": "update `teams` set `icon` = 'images/icon-68a45a4f2d281.png', `logo` = 'images/logo-68a45a4f2d0df.png', `manager` = 'Saiful Islam', `workday` = '[\\\"Tuesday\\\",\\\"Wednesday\\\",\\\"Thursday\\\",\\\"Friday\\\",\\\"Monday\\\"]', `launch` = '2025-08-19', `updated_by` = 150, `teams`.`updated_at` = '2025-08-19 17:04:47' where `id` = 43", "type": "query", "params": [], "bindings": ["images/icon-68a45a4f2d281.png", "images/logo-68a45a4f2d0df.png", "Saiful Islam", "[\"Tuesday\",\"Wednesday\",\"Thursday\",\"Friday\",\"Monday\"]", "2025-08-19", 150, "2025-08-19 17:04:47", 43], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/TeamController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\TeamController.php", "line": 427}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.1990938, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "TeamController.php:427", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/TeamController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\TeamController.php", "line": 427}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fapp%2FHttp%2FControllers%2FTeamController.php&line=427", "ajax": false, "filename": "TeamController.php", "line": "427"}, "connection": "creative_app3", "explain": null, "start_percent": 63.974, "width_percent": 14.231}, {"sql": "select * from `departments` where `departments`.`id` = 8 limit 1", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/TeamController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\TeamController.php", "line": 434}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.2018888, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "TeamController.php:434", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/TeamController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\TeamController.php", "line": 434}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fapp%2FHttp%2FControllers%2FTeamController.php&line=434", "ajax": false, "filename": "TeamController.php", "line": "434"}, "connection": "creative_app3", "explain": null, "start_percent": 78.205, "width_percent": 3.333}, {"sql": "select `teams`.*, `department_team`.`department_id` as `pivot_department_id`, `department_team`.`team_id` as `pivot_team_id` from `teams` inner join `department_team` on `teams`.`id` = `department_team`.`team_id` where `department_team`.`department_id` = 8", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/TeamController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\TeamController.php", "line": 435}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.20318, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "TeamController.php:435", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/TeamController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\TeamController.php", "line": 435}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fapp%2FHttp%2FControllers%2FTeamController.php&line=435", "ajax": false, "filename": "TeamController.php", "line": "435"}, "connection": "creative_app3", "explain": null, "start_percent": 81.538, "width_percent": 5.769}, {"sql": "insert into `department_team` (`department_id`, `team_id`) values (8, 43)", "type": "query", "params": [], "bindings": [8, 43], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "app/Http/Controllers/TeamController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\TeamController.php", "line": 436}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.2050872, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "TeamController.php:436", "source": {"index": 11, "namespace": null, "name": "app/Http/Controllers/TeamController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\TeamController.php", "line": 436}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fapp%2FHttp%2FControllers%2FTeamController.php&line=436", "ajax": false, "filename": "TeamController.php", "line": "436"}, "connection": "creative_app3", "explain": null, "start_percent": 87.308, "width_percent": 12.692}]}, "models": {"data": {"App\\Models\\Team": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fapp%2FModels%2FTeam.php&line=1", "ajax": false, "filename": "Team.php", "line": "?"}}, "Laravel\\Sanctum\\PersonalAccessToken": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FPersonalAccessToken.php&line=1", "ajax": false, "filename": "PersonalAccessToken.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Department": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fapp%2FModels%2FDepartment.php&line=1", "ajax": false, "filename": "Department.php", "line": "?"}}}, "count": 9, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": [], "request": {"path_info": "/api/teams/43", "status_code": "<pre class=sf-dump id=sf-dump-144533514 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-144533514\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1536423261 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1536423261\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:11</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">NeverGiveUp</span>\"\n  \"<span class=sf-dump-key>department_id</span>\" => <span class=sf-dump-num>8</span>\n  \"<span class=sf-dump-key>launch</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-08-19</span>\"\n  \"<span class=sf-dump-key>workday</span>\" => \"<span class=sf-dump-str title=\"52 characters\">[&quot;Tuesday&quot;,&quot;Wednesday&quot;,&quot;Thursday&quot;,&quot;Friday&quot;,&quot;Monday&quot;]</span>\"\n  \"<span class=sf-dump-key>billable_hours</span>\" => <span class=sf-dump-num>20</span>\n  \"<span class=sf-dump-key>poc</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Saiful Islam</span>\"\n  \"<span class=sf-dump-key>manager</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Saiful Islam</span>\"\n  \"<span class=sf-dump-key>team_lead</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Rajib Shaikh</span>\"\n  \"<span class=sf-dump-key>updated_by</span>\" => \"<span class=sf-dump-str title=\"3 characters\">150</span>\"\n  \"<span class=sf-dump-key>icon</span>\" => \"<span class=sf-dump-str title=\"11106 characters\">data:image/png;base64,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</span>\"\n  \"<span class=sf-dump-key>logo</span>\" => \"<span class=sf-dump-str title=\"17778 characters\">data:image/png;base64,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**************************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</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">29153</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">Bearer 444|e******</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:3000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost:3000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-904692873 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-904692873\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-494616609 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 11:04:47 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:3000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">GET, POST, PUT, DELETE, OPTIONS</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"45 characters\">Content-Type, Authorization, X-Requested-With</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-limit</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">150</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-remaining</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">148</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-494616609\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1776550793 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1776550793\", {\"maxDepth\":0})</script>\n"}}