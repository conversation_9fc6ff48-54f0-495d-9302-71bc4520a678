// import React, { useEffect, useState } from "react";
// import { fetchWeatherApi } from "openmeteo";
// import CustomClock from "./CustomClock";

// const WeatherData = () => {
//     const [weatherData, setWeatherData] = useState(null);
//     const [loading, setLoading] = useState(true);
//     const [error, setError] = useState(null);
//     const [ipData, setIpData] = useState(false);
//     const [data5Days, set5DaysData] = useState(false);
//     // Fetch IP data
//     useEffect(() => {
//         fetch("https://ip-api.com/json/")
//             .then((response) => response.json())
//             .then((data) => {
//                 setIpData(data);
//             })
//             .catch((err) => setError(err));
//     }, []);
//     const params = {
//         latitude: ipData?.lat,
//         longitude: ipData?.lon,
//         hourly: [
//             "temperature_2m", "relative_humidity_2m", "apparent_temperature", "precipitation_probability", "precipitation", "rain", "visibility", "wind_speed_10m", "uv_index",
//         ],
//         daily: ["weather_code", "sunrise", "sunset"],
//         timeformat: "unixtime",
//         timezone: "auto",
//         past_days: 0,
//         forecast_days: 6,
//     };
//     function getRoundedHour(unixTimestamp) {
//         // Create a date object from the Unix timestamp (in seconds)
//         const date = new Date(unixTimestamp * 1000);

//         // Get hours, minutes, and seconds
//         const hours = date.getUTCHours() + 6;
//         const minutes = date.getUTCMinutes();
//         const seconds = date.getUTCSeconds();

//         // Determine the rounded hour
//         let roundedHour;
//         if (minutes > 31 || seconds > 31) {
//             roundedHour = hours + 1; // Round up to the next hour
//         } else {
//             roundedHour = hours; // Keep the same hour
//         }

//         // Return the rounded hour (in 24-hour format)
//         return roundedHour % 24; // Ensure it wraps around at 24
//     }

//     // Example usage
//     const unixTime = Math.floor(Date.now() / 1000); // Example Unix timestamp
//     const roundedHour = getRoundedHour(unixTime);

//     const currentTime = Math.floor(Date.now() / 1000);
//     const isoString = new Date(currentTime * 1000).toISOString();

//     useEffect(() => {
//         if (!ipData) return;
//         const fetchWeather = async () => {
//             try {
//                 const url = "https://api.open-meteo.com/v1/forecast";
//                 const responses = await fetchWeatherApi(url, params);
//                 const range = (start, stop, step) =>
//                     Array.from({ length: (stop - start) / step }, (_, i) => start + i * step);
//                 const response = responses[0];

//                 const utcOffsetSeconds = response.utcOffsetSeconds();

//                 const hourly = response.hourly();
//                 const daily = response.daily();

//                 const weather = {
//                     hourly: {
//                         time: range(
//                             Number(hourly.time()),
//                             Number(hourly.timeEnd()),
//                             hourly.interval()
//                         ).map((t) => new Date((t + utcOffsetSeconds) * 1000)),
//                         temperature2m: hourly.variables(0).valuesArray(),
//                         relativeHumidity2m: hourly.variables(1).valuesArray(),
//                         apparentTemperature: hourly.variables(2).valuesArray(),
//                         precipitationProbability: hourly.variables(3).valuesArray(),
//                         precipitation: hourly.variables(4).valuesArray(),
//                         rain: hourly.variables(5).valuesArray(),
//                         visibility: hourly.variables(6).valuesArray(),
//                         windSpeed10m: hourly.variables(7).valuesArray(),
//                         uvIndex: hourly.variables(8).valuesArray(),
//                     },
//                     daily: {
//                         time: range(
//                             Number(daily.time()),
//                             Number(daily.timeEnd()),
//                             daily.interval()
//                         ).map((t) => new Date((t + utcOffsetSeconds) * 1000)),
//                         weatherCode: daily.variables(0).valuesArray(),
//                         sunrise: daily.variables(1).valuesArray(),
//                         sunset: daily.variables(2).valuesArray(),
//                     },
//                 };

//                 setWeatherData(weather);
//             } catch (err) {
//                 setError(err);
//             } finally {
//                 setLoading(false);
//             }
//         };
//         fetchWeather();
//     }, [ipData]);

//     useEffect(() => {
//         // filter 5days data
//         if (!weatherData) return;
//         const getFivePMData = (data) => {
//             const result = [];

//             // 5 PM index is 17, calculate the indices for 6 days (6 * 24 = 144)
//             const fivePMIndex = roundedHour;

//             // Set here day 1 to avoid today's data
//             for (let day = 1; day < 6; day++) {
//                 const index = day * 24 + fivePMIndex;
//                 result.push({
//                     dayName: data.daily.time[day].toLocaleString('default', { weekday: 'long' }), // Get day name
//                     date: data.daily.time[day].toLocaleString('default', { day: 'numeric', month: 'long' }), // Get date month
//                     temperature: data.hourly.temperature2m[index], // Temperature
//                     feelTemperature: data.hourly.apparentTemperature[index], // Feels-like temperature
//                 });
//             }

//             return result;
//         };
//         // set 5days Temperature
//         set5DaysData(getFivePMData(weatherData))
//     }, [ipData, weatherData]);

//     if (loading) return <div>Loading...</div>;
//     if (error) return <div>Error: {error.message}</div>;
//     // console.log(data5Days);
//     const hourlyData = [];
//     for (let i = roundedHour; i < roundedHour + 12; i++) {
//         hourlyData.push(
//             <div className="flex flex-col" key={i}>
//                 <p>{i > 12 && i < 25 ? `${(i - 12)} PM` : `${i > 24 ? (i - 24) : { i }} AM`}</p>
//                 <p>{Math.round(weatherData.hourly.temperature2m[i])}°C</p>
//             </div>
//         );
//     }
//     return (
//         <div>
//             <CustomClock ipData={ipData} />
//             <div className="">
//                 <div className="grid grid-cols-2 gap-4 w-100" >
//                     <div className="">
//                         <h5>{`${ipData?.city}, ${ipData?.country}`}</h5>
//                         <h1>{Math.round(weatherData.hourly.temperature2m[roundedHour])}°C </h1>
//                         <div className="flex row">
//                             <div className="col-4">
//                                 <p>
//                                     Humidity: {weatherData.hourly.relativeHumidity2m[roundedHour]}%
//                                 </p>
//                             </div>
//                             <div className="col-4">
//                                 <p>
//                                     feels like:{" "}
//                                     {Math.round(weatherData.hourly.apparentTemperature[roundedHour])}°C
//                                 </p>
//                             </div>
//                             <div className="col-4">
//                                 <p>
//                                     Chance of Rain:{" "}
//                                     {weatherData.hourly.precipitationProbability[roundedHour]}%
//                                 </p>
//                             </div>
//                             <div className="col-4">
//                                 <p>
//                                     Visibility: {weatherData.hourly.visibility[roundedHour] / 1000} km
//                                 </p>
//                             </div>
//                             <div className="col-4">
//                                 <p>
//                                     UV: {" "}
//                                     {Math.round(weatherData.hourly.uvIndex[roundedHour])}
//                                 </p>
//                             </div>
//                             <div className="col-4">
//                                 <p>
//                                     Wind Speed:{" "}
//                                     {Math.round(weatherData.hourly.windSpeed10m[roundedHour])} km/h
//                                 </p>
//                             </div>
//                         </div>
//                     </div>
//                     <div className="col-4 pl-6" style={{ gap: '0px', borderLeft: "5px solid green", marginTop: "20px" }}>
//                         {data5Days && <h4>
//                             {data5Days.length} Days Forecast
//                         </h4>
//                         }
//                         {data5Days && data5Days?.map((item, index) => {
//                             return (
//                                 <div className="flex flex-row" style={{ marginTop: "10px", alignItems: 'center' }}>
//                                     <div className="col-6">
//                                         <p>
//                                             {item.date}{" "}
//                                         </p><br />
//                                         <p sx={{ fontWeight: 'bold' }}>
//                                             {item.dayName}
//                                         </p>
//                                     </div>
//                                     <div className="col-6" style={{ textAlign: "center" }}>
//                                         <p>
//                                             {Math.round(item.temperature)}°C
//                                         </p>
//                                     </div>
//                                 </div>
//                             )
//                         })
//                         }
//                     </div>
//                 </div>
//                 <div className="flex gap-8 pt-6 w-full overflow-x-auto">
//                     {hourlyData}
//                 </div>
//             </div>
//         </div>
//     );
// };

// export default WeatherData;
import React, { useEffect, useState } from "react";
import { fetchWeatherApi } from "openmeteo";
import CustomClock from "./CustomClock";

const WeatherData = () => {
    const [weatherData, setWeatherData] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [ipData, setIpData] = useState(null); // Changed to null for better checking
    const [data5Days, set5DaysData] = useState(null); // Changed to null

    // Fetch IP data using reallyfreegeoip.org
    useEffect(() => {
        fetch("https://reallyfreegeoip.org/json")
            .then((response) => response.json())
            .then((data) => {
                setIpData(data);
            })
            .catch((err) => setError(err));
    }, []);

    const params = {
        latitude: ipData?.latitude,
        longitude: ipData?.longitude,
        hourly: [
            "temperature_2m", "relative_humidity_2m", "apparent_temperature", "precipitation_probability", "precipitation", "rain", "visibility", "wind_speed_10m", "uv_index",
        ],
        daily: ["weather_code", "sunrise", "sunset"],
        timeformat: "unixtime",
        timezone: "auto",
        past_days: 0,
        forecast_days: 6,
    };

    function getRoundedHour(unixTimestamp) {
        // Create a date object from the Unix timestamp (in seconds)
        const date = new Date(unixTimestamp * 1000);

        // Get hours, minutes, and seconds
        const hours = date.getUTCHours() + 6;
        const minutes = date.getUTCMinutes();
        const seconds = date.getUTCSeconds();

        // Determine the rounded hour
        let roundedHour;
        if (minutes > 31 || (minutes === 31 && seconds > 31)) { // Adjusted for precision
            roundedHour = hours + 1; // Round up to the next hour
        } else {
            roundedHour = hours; // Keep the same hour
        }

        // Return the rounded hour (in 24-hour format)
        return roundedHour % 24; // Ensure it wraps around at 24
    }

    // Example usage
    const unixTime = Math.floor(Date.now() / 1000); // Example Unix timestamp
    const roundedHour = getRoundedHour(unixTime);

    useEffect(() => {
        if (!ipData) return;
        const fetchWeather = async () => {
            try {
                const url = "https://api.open-meteo.com/v1/forecast";
                const responses = await fetchWeatherApi(url, params);
                const range = (start, stop, step) =>
                    Array.from({ length: (stop - start) / step }, (_, i) => start + i * step);
                const response = responses[0];

                const utcOffsetSeconds = response.utcOffsetSeconds();

                const hourly = response.hourly();
                const daily = response.daily();

                const weather = {
                    hourly: {
                        time: range(
                            Number(hourly.time()),
                            Number(hourly.timeEnd()),
                            hourly.interval()
                        ).map((t) => new Date((t + utcOffsetSeconds) * 1000)),
                        temperature2m: hourly.variables(0).valuesArray(),
                        relativeHumidity2m: hourly.variables(1).valuesArray(),
                        apparentTemperature: hourly.variables(2).valuesArray(),
                        precipitationProbability: hourly.variables(3).valuesArray(),
                        precipitation: hourly.variables(4).valuesArray(),
                        rain: hourly.variables(5).valuesArray(),
                        visibility: hourly.variables(6).valuesArray(),
                        windSpeed10m: hourly.variables(7).valuesArray(),
                        uvIndex: hourly.variables(8).valuesArray(),
                    },
                    daily: {
                        time: range(
                            Number(daily.time()),
                            Number(daily.timeEnd()),
                            daily.interval()
                        ).map((t) => new Date((t + utcOffsetSeconds) * 1000)),
                        weatherCode: daily.variables(0).valuesArray(),
                        sunrise: daily.variables(1).valuesArray(),
                        sunset: daily.variables(2).valuesArray(),
                    },
                };

                setWeatherData(weather);
            } catch (err) {
                setError(err);
            } finally {
                setLoading(false);
            }
        };
        fetchWeather();
    }, [ipData]);

    useEffect(() => {
        // filter 5days data
        if (!weatherData) return;
        const getFivePMData = (data) => {
            const result = [];

            // Set the index based on roundedHour
            const targetHourIndex = roundedHour;

            // Loop for days 1 to 5 (skipping today)
            for (let day = 1; day < 6; day++) {
                const index = day * 24 + targetHourIndex;
                result.push({
                    dayName: data.daily.time[day].toLocaleString('default', { weekday: 'long' }),
                    date: data.daily.time[day].toLocaleString('default', { day: 'numeric', month: 'long' }),
                    temperature: data.hourly.temperature2m[index],
                    feelTemperature: data.hourly.apparentTemperature[index],
                });
            }

            return result;
        };
        // set 5days Temperature
        set5DaysData(getFivePMData(weatherData));
    }, [weatherData]); // Removed ipData from dependencies as it's not used here

    if (loading) return <div>Loading...</div>;
    if (error) return <div>Error: {error.message}</div>;

    const hourlyData = [];
    for (let i = roundedHour; i < roundedHour + 12; i++) {
        const hour = i % 24; // Wrap around 24 hours
        const amPm = hour >= 12 ? 'PM' : 'AM';
        const displayHour = hour % 12 === 0 ? 12 : hour % 12;
        hourlyData.push(
            <div className="flex flex-col" key={i}>
                <p>{`${displayHour} ${amPm}`}</p>
                <p>{Math.round(weatherData.hourly.temperature2m[i])}°C</p>
            </div>
        );
    }

    return (
        <div>
            <CustomClock ipData={ipData} />
            <div className="">
                <div className="grid grid-cols-2 gap-4 w-100">
                    <div className="">
                        <h5>{`${ipData?.city}, ${ipData?.country_name}`}</h5> {/* Updated country to country_name */}
                        <h1>{Math.round(weatherData.hourly.temperature2m[roundedHour])}°C </h1>
                        <div className="flex row">
                            <div className="col-4">
                                <p>
                                    Humidity: {weatherData.hourly.relativeHumidity2m[roundedHour]}%
                                </p>
                            </div>
                            <div className="col-4">
                                <p>
                                    feels like:{" "}
                                    {Math.round(weatherData.hourly.apparentTemperature[roundedHour])}°C
                                </p>
                            </div>
                            <div className="col-4">
                                <p>
                                    Chance of Rain:{" "}
                                    {weatherData.hourly.precipitationProbability[roundedHour]}%
                                </p>
                            </div>
                            <div className="col-4">
                                <p>
                                    Visibility: {weatherData.hourly.visibility[roundedHour] / 1000} km
                                </p>
                            </div>
                            <div className="col-4">
                                <p>
                                    UV: {" "}
                                    {Math.round(weatherData.hourly.uvIndex[roundedHour])}
                                </p>
                            </div>
                            <div className="col-4">
                                <p>
                                    Wind Speed:{" "}
                                    {Math.round(weatherData.hourly.windSpeed10m[roundedHour])} km/h
                                </p>
                            </div>
                        </div>
                    </div>
                    <div className="col-4 pl-6" style={{ gap: '0px', borderLeft: "5px solid green", marginTop: "20px" }}>
                        {data5Days && <h4>
                            {data5Days.length} Days Forecast
                        </h4>
                        }
                        {data5Days && data5Days.map((item, index) => (
                            <div className="flex flex-row" style={{ marginTop: "10px", alignItems: 'center' }} key={index}>
                                <div className="col-6">
                                    <p>
                                        {item.date}{" "}
                                    </p><br />
                                    <p style={{ fontWeight: 'bold' }}>
                                        {item.dayName}
                                    </p>
                                </div>
                                <div className="col-6" style={{ textAlign: "center" }}>
                                    <p>
                                        {Math.round(item.temperature)}°C
                                    </p>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
                <div className="flex gap-8 pt-6 w-full overflow-x-auto">
                    {hourlyData}
                </div>
            </div>
        </div>
    );
};

export default WeatherData;