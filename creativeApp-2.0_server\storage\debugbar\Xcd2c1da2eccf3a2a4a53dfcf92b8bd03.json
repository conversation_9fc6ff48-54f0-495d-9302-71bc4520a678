{"__meta": {"id": "Xcd2c1da2eccf3a2a4a53dfcf92b8bd03", "datetime": "2025-08-19 15:12:14", "utime": 1755594734.076773, "method": "GET", "uri": "/api/time-cards-data?per_page=1000", "ip": "127.0.0.1"}, "php": {"version": "8.0.30", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[15:12:13] LOG.info: Authenticated user roles: [\n    \"super-admin\"\n]", "message_html": null, "is_string": false, "label": "info", "time": **********.229189, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1755594732.717321, "end": 1755594734.076792, "duration": 1.359471082687378, "duration_str": "1.36s", "measures": [{"label": "Booting", "start": 1755594732.717321, "relative_start": 0, "end": **********.178053, "relative_end": **********.178053, "duration": 0.*****************, "duration_str": "461ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.178065, "relative_start": 0.*****************, "end": 1755594734.076794, "relative_end": 1.9073486328125e-06, "duration": 0.8987288475036621, "duration_str": "899ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 29515752, "peak_usage_str": "28MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET api/time-cards-data", "middleware": "api, auth:sanctum, cors, verified, role:super-admin|admin", "controller": "App\\Http\\Controllers\\TimeCardController@timeCardsData", "namespace": null, "prefix": "api", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fapp%2FHttp%2FControllers%2FTimeCardController.php&line=96\" onclick=\"\">app/Http/Controllers/TimeCardController.php:96-555</a>"}, "queries": {"nb_statements": 30, "nb_visible_statements": 31, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.024350000000000004, "accumulated_duration_str": "24.35ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/sanctum/src/PersonalAccessToken.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php", "line": 66}, {"index": 14, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 67}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 42}], "start": **********.207524, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "PersonalAccessToken.php:66", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/sanctum/src/PersonalAccessToken.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php", "line": 66}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FPersonalAccessToken.php&line=66", "ajax": false, "filename": "PersonalAccessToken.php", "line": "66"}, "connection": "creative_app3", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from `personal_access_tokens` where `personal_access_tokens`.`id` = '444' limit 1", "type": "query", "params": [], "bindings": ["444"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/laravel/sanctum/src/PersonalAccessToken.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php", "line": 66}, {"index": 20, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 67}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 24, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 42}], "start": **********.213032, "duration": 0.00216, "duration_str": "2.16ms", "memory": 0, "memory_str": null, "filename": "PersonalAccessToken.php:66", "source": {"index": 19, "namespace": null, "name": "vendor/laravel/sanctum/src/PersonalAccessToken.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php", "line": 66}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FPersonalAccessToken.php&line=66", "ajax": false, "filename": "PersonalAccessToken.php", "line": "66"}, "connection": "creative_app3", "explain": null, "start_percent": 0, "width_percent": 8.871}, {"sql": "select * from `users` where `users`.`id` = 150 limit 1", "type": "query", "params": [], "bindings": [150], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 161}, {"index": 21, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 69}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}, {"index": 26, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 42}], "start": **********.2185318, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "Guard.php:161", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 161}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FGuard.php&line=161", "ajax": false, "filename": "Guard.php", "line": "161"}, "connection": "creative_app3", "explain": null, "start_percent": 8.871, "width_percent": 1.027}, {"sql": "update `personal_access_tokens` set `last_used_at` = '2025-08-19 15:12:13', `personal_access_tokens`.`updated_at` = '2025-08-19 15:12:13' where `id` = 444", "type": "query", "params": [], "bindings": ["2025-08-19 15:12:13", "2025-08-19 15:12:13", 444], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 83}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 42}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}], "start": **********.220795, "duration": 0.00156, "duration_str": "1.56ms", "memory": 0, "memory_str": null, "filename": "Guard.php:83", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 83}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FGuard.php&line=83", "ajax": false, "filename": "Guard.php", "line": "83"}, "connection": "creative_app3", "explain": null, "start_percent": 9.897, "width_percent": 6.407}, {"sql": "select `name` from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `role_user`.`user_id` = 150", "type": "query", "params": [], "bindings": [150], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": "middleware", "name": "role", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Middleware\\RoleMiddleware.php", "line": 18}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 18, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 30}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 20, "namespace": "middleware", "name": "cors", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Middleware\\Cors.php", "line": 20}], "start": **********.227466, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "role:18", "source": {"index": 16, "namespace": "middleware", "name": "role", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Middleware\\RoleMiddleware.php", "line": 18}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fapp%2FHttp%2FMiddleware%2FRoleMiddleware.php&line=18", "ajax": false, "filename": "RoleMiddleware.php", "line": "18"}, "connection": "creative_app3", "explain": null, "start_percent": 16.304, "width_percent": 1.314}, {"sql": "select exists(select * from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `role_user`.`user_id` = 150 and `name` in ('super-admin', 'admin', 'hod', 'manager', 'team-lead', 'coordinator', 'shift-lead', 'team-member', 'guest')) as `exists`", "type": "query", "params": [], "bindings": [150, "super-admin", "admin", "hod", "manager", "team-lead", "coordinator", "shift-lead", "team-member", "guest"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": "middleware", "name": "role", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Middleware\\RoleMiddleware.php", "line": 21}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 15, "namespace": "middleware", "name": "verified", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 30}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 17, "namespace": "middleware", "name": "cors", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Middleware\\Cors.php", "line": 20}], "start": **********.229396, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "role:21", "source": {"index": 13, "namespace": "middleware", "name": "role", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Middleware\\RoleMiddleware.php", "line": 21}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fapp%2FHttp%2FMiddleware%2FRoleMiddleware.php&line=21", "ajax": false, "filename": "RoleMiddleware.php", "line": "21"}, "connection": "creative_app3", "explain": null, "start_percent": 17.618, "width_percent": 1.109}, {"sql": "select exists(select * from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `role_user`.`user_id` = 150 and `name` in ('super-admin', 'admin', 'manager', 'coordinator', 'shift-lead')) as `exists`", "type": "query", "params": [], "bindings": [150, "super-admin", "admin", "manager", "coordinator", "shift-lead"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/TimeCardController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\TimeCardController.php", "line": 150}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.23114, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "TimeCardController.php:150", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/TimeCardController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\TimeCardController.php", "line": 150}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fapp%2FHttp%2FControllers%2FTimeCardController.php&line=150", "ajax": false, "filename": "TimeCardController.php", "line": "150"}, "connection": "creative_app3", "explain": null, "start_percent": 18.727, "width_percent": 0.862}, {"sql": "select count(*) as aggregate from `time_cards`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/TimeCardController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\TimeCardController.php", "line": 440}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.2326171, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "TimeCardController.php:440", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/TimeCardController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\TimeCardController.php", "line": 440}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fapp%2FHttp%2FControllers%2FTimeCardController.php&line=440", "ajax": false, "filename": "TimeCardController.php", "line": "440"}, "connection": "creative_app3", "explain": null, "start_percent": 19.589, "width_percent": 2.587}, {"sql": "select * from `time_cards` order by `created_at` desc limit 1000 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/TimeCardController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\TimeCardController.php", "line": 440}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.234268, "duration": 0.00523, "duration_str": "5.23ms", "memory": 0, "memory_str": null, "filename": "TimeCardController.php:440", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/TimeCardController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\TimeCardController.php", "line": 440}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fapp%2FHttp%2FControllers%2FTimeCardController.php&line=440", "ajax": false, "filename": "TimeCardController.php", "line": "440"}, "connection": "creative_app3", "explain": null, "start_percent": 22.177, "width_percent": 21.478}, {"sql": "select * from `users` where `users`.`id` in (67, 83, 143, 144, 147)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/TimeCardController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\TimeCardController.php", "line": 440}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.2460039, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "TimeCardController.php:440", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/TimeCardController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\TimeCardController.php", "line": 440}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fapp%2FHttp%2FControllers%2FTimeCardController.php&line=440", "ajax": false, "filename": "TimeCardController.php", "line": "440"}, "connection": "creative_app3", "explain": null, "start_percent": 43.655, "width_percent": 2.218}, {"sql": "select * from `users` where `users`.`id` in (83, 143, 144)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/TimeCardController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\TimeCardController.php", "line": 440}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.2485, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "TimeCardController.php:440", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/TimeCardController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\TimeCardController.php", "line": 440}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fapp%2FHttp%2FControllers%2FTimeCardController.php&line=440", "ajax": false, "filename": "TimeCardController.php", "line": "440"}, "connection": "creative_app3", "explain": null, "start_percent": 45.873, "width_percent": 1.314}, {"sql": "select * from `teams` where `teams`.`id` in (28)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/TimeCardController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\TimeCardController.php", "line": 440}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.250837, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "TimeCardController.php:440", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/TimeCardController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\TimeCardController.php", "line": 440}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fapp%2FHttp%2FControllers%2FTimeCardController.php&line=440", "ajax": false, "filename": "TimeCardController.php", "line": "440"}, "connection": "creative_app3", "explain": null, "start_percent": 47.187, "width_percent": 0.657}, {"sql": "select * from `departments` where `departments`.`id` in (7)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/TimeCardController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\TimeCardController.php", "line": 440}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.253402, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "TimeCardController.php:440", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/TimeCardController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\TimeCardController.php", "line": 440}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fapp%2FHttp%2FControllers%2FTimeCardController.php&line=440", "ajax": false, "filename": "TimeCardController.php", "line": "440"}, "connection": "creative_app3", "explain": null, "start_percent": 47.844, "width_percent": 2.423}, {"sql": "select * from `schedules` where `schedules`.`id` in (6)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/TimeCardController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\TimeCardController.php", "line": 440}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.261633, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "TimeCardController.php:440", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/TimeCardController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\TimeCardController.php", "line": 440}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fapp%2FHttp%2FControllers%2FTimeCardController.php&line=440", "ajax": false, "filename": "TimeCardController.php", "line": "440"}, "connection": "creative_app3", "explain": null, "start_percent": 50.267, "width_percent": 1.766}, {"sql": "select * from `product_types` where `product_types`.`id` in (14, 15, 16, 22)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/TimeCardController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\TimeCardController.php", "line": 440}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.2643352, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "TimeCardController.php:440", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/TimeCardController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\TimeCardController.php", "line": 440}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fapp%2FHttp%2FControllers%2FTimeCardController.php&line=440", "ajax": false, "filename": "TimeCardController.php", "line": "440"}, "connection": "creative_app3", "explain": null, "start_percent": 52.033, "width_percent": 2.628}, {"sql": "select * from `task_types` where `task_types`.`id` in (10, 12, 17)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/TimeCardController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\TimeCardController.php", "line": 440}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.267201, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "TimeCardController.php:440", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/TimeCardController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\TimeCardController.php", "line": 440}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fapp%2FHttp%2FControllers%2FTimeCardController.php&line=440", "ajax": false, "filename": "TimeCardController.php", "line": "440"}, "connection": "creative_app3", "explain": null, "start_percent": 54.661, "width_percent": 2.094}, {"sql": "select * from `revision_types` where `revision_types`.`id` in (7, 8, 12)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/TimeCardController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\TimeCardController.php", "line": 440}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.2722762, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "TimeCardController.php:440", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/TimeCardController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\TimeCardController.php", "line": 440}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fapp%2FHttp%2FControllers%2FTimeCardController.php&line=440", "ajax": false, "filename": "TimeCardController.php", "line": "440"}, "connection": "creative_app3", "explain": null, "start_percent": 56.756, "width_percent": 4.887}, {"sql": "select * from `record_types` where `record_types`.`id` in (11, 12)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/TimeCardController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\TimeCardController.php", "line": 440}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.275844, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "TimeCardController.php:440", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/TimeCardController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\TimeCardController.php", "line": 440}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fapp%2FHttp%2FControllers%2FTimeCardController.php&line=440", "ajax": false, "filename": "TimeCardController.php", "line": "440"}, "connection": "creative_app3", "explain": null, "start_percent": 61.643, "width_percent": 2.546}, {"sql": "select * from `regions` where `regions`.`id` in (8)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/TimeCardController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\TimeCardController.php", "line": 440}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.2789872, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "TimeCardController.php:440", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/TimeCardController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\TimeCardController.php", "line": 440}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fapp%2FHttp%2FControllers%2FTimeCardController.php&line=440", "ajax": false, "filename": "TimeCardController.php", "line": "440"}, "connection": "creative_app3", "explain": null, "start_percent": 64.189, "width_percent": 3.285}, {"sql": "select * from `priorities` where `priorities`.`id` in (10, 11, 13)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/TimeCardController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\TimeCardController.php", "line": 440}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.282494, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "TimeCardController.php:440", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/TimeCardController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\TimeCardController.php", "line": 440}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fapp%2FHttp%2FControllers%2FTimeCardController.php&line=440", "ajax": false, "filename": "TimeCardController.php", "line": "440"}, "connection": "creative_app3", "explain": null, "start_percent": 67.474, "width_percent": 2.177}, {"sql": "select * from `reporters` where `reporters`.`id` in (13, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/TimeCardController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\TimeCardController.php", "line": 440}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.285337, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "TimeCardController.php:440", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/TimeCardController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\TimeCardController.php", "line": 440}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fapp%2FHttp%2FControllers%2FTimeCardController.php&line=440", "ajax": false, "filename": "TimeCardController.php", "line": "440"}, "connection": "creative_app3", "explain": null, "start_percent": 69.651, "width_percent": 2.3}, {"sql": "select * from `review_releases` where `review_releases`.`id` in (1, 3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/TimeCardController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\TimeCardController.php", "line": 440}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.291852, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "TimeCardController.php:440", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/TimeCardController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\TimeCardController.php", "line": 440}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fapp%2FHttp%2FControllers%2FTimeCardController.php&line=440", "ajax": false, "filename": "TimeCardController.php", "line": "440"}, "connection": "creative_app3", "explain": null, "start_percent": 71.951, "width_percent": 2.998}, {"sql": "select count(distinct `date`) as aggregate from `time_cards`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/TimeCardController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\TimeCardController.php", "line": 447}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.296032, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "TimeCardController.php:447", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/TimeCardController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\TimeCardController.php", "line": 447}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fapp%2FHttp%2FControllers%2FTimeCardController.php&line=447", "ajax": false, "filename": "TimeCardController.php", "line": "447"}, "connection": "creative_app3", "explain": null, "start_percent": 74.949, "width_percent": 2.341}, {"sql": "select count(distinct `user_id`) as aggregate from `time_cards`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/TimeCardController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\TimeCardController.php", "line": 451}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.298651, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "TimeCardController.php:451", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/TimeCardController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\TimeCardController.php", "line": 451}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fapp%2FHttp%2FControllers%2FTimeCardController.php&line=451", "ajax": false, "filename": "TimeCardController.php", "line": "451"}, "connection": "creative_app3", "explain": null, "start_percent": 77.29, "width_percent": 1.766}, {"sql": "select count(distinct `ticket`) as aggregate from `time_cards`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/TimeCardController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\TimeCardController.php", "line": 459}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.301133, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "TimeCardController.php:459", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/TimeCardController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\TimeCardController.php", "line": 459}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fapp%2FHttp%2FControllers%2FTimeCardController.php&line=459", "ajax": false, "filename": "TimeCardController.php", "line": "459"}, "connection": "creative_app3", "explain": null, "start_percent": 79.055, "width_percent": 2.834}, {"sql": "select sum(`unit`) as aggregate from `time_cards`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/TimeCardController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\TimeCardController.php", "line": 460}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.304688, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "TimeCardController.php:460", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/TimeCardController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\TimeCardController.php", "line": 460}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fapp%2FHttp%2FControllers%2FTimeCardController.php&line=460", "ajax": false, "filename": "TimeCardController.php", "line": "460"}, "connection": "creative_app3", "explain": null, "start_percent": 81.889, "width_percent": 3.121}, {"sql": "select `hour` from `time_cards` where `hour` != '00:00:00'", "type": "query", "params": [], "bindings": ["00:00:00"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/TimeCardController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\TimeCardController.php", "line": 462}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.3084621, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "TimeCardController.php:462", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/TimeCardController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\TimeCardController.php", "line": 462}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fapp%2FHttp%2FControllers%2FTimeCardController.php&line=462", "ajax": false, "filename": "TimeCardController.php", "line": "462"}, "connection": "creative_app3", "explain": null, "start_percent": 85.01, "width_percent": 4.189}, {"sql": "select `hour` from `time_cards` where `hour` != '00:00:00' and `task_type_id` = 17 and `hour` != '00:00:00'", "type": "query", "params": [], "bindings": ["00:00:00", 17, "00:00:00"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/TimeCardController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\TimeCardController.php", "line": 487}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.3119779, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "TimeCardController.php:487", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/TimeCardController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\TimeCardController.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fapp%2FHttp%2FControllers%2FTimeCardController.php&line=487", "ajax": false, "filename": "TimeCardController.php", "line": "487"}, "connection": "creative_app3", "explain": null, "start_percent": 89.199, "width_percent": 2.3}, {"sql": "select sum(`client_error`) as aggregate from `time_cards` where `hour` != '00:00:00' and `task_type_id` = 17 and `hour` != '00:00:00'", "type": "query", "params": [], "bindings": ["00:00:00", 17, "00:00:00"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/TimeCardController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\TimeCardController.php", "line": 501}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.314783, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "TimeCardController.php:501", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/TimeCardController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\TimeCardController.php", "line": 501}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fapp%2FHttp%2FControllers%2FTimeCardController.php&line=501", "ajax": false, "filename": "TimeCardController.php", "line": "501"}, "connection": "creative_app3", "explain": null, "start_percent": 91.499, "width_percent": 1.971}, {"sql": "select sum(`internal_error`) as aggregate from `time_cards` where `hour` != '00:00:00' and `task_type_id` = 17 and `hour` != '00:00:00'", "type": "query", "params": [], "bindings": ["00:00:00", 17, "00:00:00"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/TimeCardController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\TimeCardController.php", "line": 502}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.3173501, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "TimeCardController.php:502", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/TimeCardController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\TimeCardController.php", "line": 502}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fapp%2FHttp%2FControllers%2FTimeCardController.php&line=502", "ajax": false, "filename": "TimeCardController.php", "line": "502"}, "connection": "creative_app3", "explain": null, "start_percent": 93.47, "width_percent": 1.807}, {"sql": "select `hour` from `time_cards` where `hour` != '00:00:00' and `task_type_id` = 17 and `hour` != '00:00:00' and `hour` != '00:00:00'", "type": "query", "params": [], "bindings": ["00:00:00", 17, "00:00:00", "00:00:00"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/TimeCardController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\TimeCardController.php", "line": 512}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.3207252, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "TimeCardController.php:512", "source": {"index": 13, "namespace": null, "name": "app/Http/Controllers/TimeCardController.php", "file": "C:\\xampp\\htdocs\\creativeApp-2.0_last\\creativeApp-2.0_server\\app\\Http\\Controllers\\TimeCardController.php", "line": 512}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fapp%2FHttp%2FControllers%2FTimeCardController.php&line=512", "ajax": false, "filename": "TimeCardController.php", "line": "512"}, "connection": "creative_app3", "explain": null, "start_percent": 95.277, "width_percent": 4.723}]}, "models": {"data": {"App\\Models\\TimeCard": {"value": 415, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fapp%2FModels%2FTimeCard.php&line=1", "ajax": false, "filename": "TimeCard.php", "line": "?"}}, "App\\Models\\Reporter": {"value": 20, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fapp%2FModels%2FReporter.php&line=1", "ajax": false, "filename": "Reporter.php", "line": "?"}}, "App\\Models\\User": {"value": 9, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\ProductType": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fapp%2FModels%2FProductType.php&line=1", "ajax": false, "filename": "ProductType.php", "line": "?"}}, "App\\Models\\TaskType": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fapp%2FModels%2FTaskType.php&line=1", "ajax": false, "filename": "TaskType.php", "line": "?"}}, "App\\Models\\RevisionType": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fapp%2FModels%2FRevisionType.php&line=1", "ajax": false, "filename": "RevisionType.php", "line": "?"}}, "App\\Models\\Priority": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fapp%2FModels%2FPriority.php&line=1", "ajax": false, "filename": "Priority.php", "line": "?"}}, "App\\Models\\RecordType": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fapp%2FModels%2FRecordType.php&line=1", "ajax": false, "filename": "RecordType.php", "line": "?"}}, "Laravel\\Sanctum\\PersonalAccessToken": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FPersonalAccessToken.php&line=1", "ajax": false, "filename": "PersonalAccessToken.php", "line": "?"}}, "App\\Models\\Region": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fxampp%2Fhtdocs%2FcreativeApp-2.0_last%2FcreativeApp-2.0_server%2Fapp%2FModels%2FRegion.php&line=1", "ajax": false, "filename": "Region.php", "line": "?"}}}, "count": 461, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": [], "request": {"path_info": "/api/time-cards-data", "status_code": "<pre class=sf-dump id=sf-dump-964746491 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-964746491\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-911711229 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>per_page</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1000</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-911711229\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1848947214 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1848947214\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-795053558 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">Bearer 444|e******</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not;A=Brand&quot;;v=&quot;99&quot;, &quot;Google Chrome&quot;;v=&quot;139&quot;, &quot;Chromium&quot;;v=&quot;139&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:3000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost:3000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-795053558\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1997331361 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1997331361\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1390013402 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 19 Aug 2025 09:12:13 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:3000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">GET, POST, PUT, DELETE, OPTIONS</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"45 characters\">Content-Type, Authorization, X-Requested-With</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-limit</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">150</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-remaining</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">141</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1390013402\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-674619692 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-674619692\", {\"maxDepth\":0})</script>\n"}}